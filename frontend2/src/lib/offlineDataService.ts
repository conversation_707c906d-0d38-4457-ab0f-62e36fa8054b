'use client';

import { api } from './api';

// Offline-first data service with local storage and sync capabilities
class OfflineDataService {
  private storagePrefix = 'vetcare_';
  private syncQueue: any[] = [];
  private isOnline = typeof window !== 'undefined' ? navigator.onLine : true;

  constructor() {
    if (typeof window !== 'undefined') {
      // Listen for online/offline events
      window.addEventListener('online', this.handleOnline.bind(this));
      window.addEventListener('offline', this.handleOffline.bind(this));
      
      // Initialize sync queue from localStorage
      this.loadSyncQueue();
    }
  }

  // Storage operations
  private getStorageKey(key: string): string {
    return `${this.storagePrefix}${key}`;
  }

  private setItem(key: string, data: any): void {
    if (typeof window === 'undefined') return;
    try {
      localStorage.setItem(this.getStorageKey(key), JSON.stringify(data));
    } catch (error) {
      console.error('Error saving to localStorage:', error);
    }
  }

  private getItem(key: string): any {
    if (typeof window === 'undefined') return null;
    try {
      const item = localStorage.getItem(this.getStorageKey(key));
      return item ? JSON.parse(item) : null;
    } catch (error) {
      console.error('Error reading from localStorage:', error);
      return null;
    }
  }

  private removeItem(key: string): void {
    if (typeof window === 'undefined') return;
    localStorage.removeItem(this.getStorageKey(key));
  }

  // Sync queue management
  private loadSyncQueue(): void {
    this.syncQueue = this.getItem('sync_queue') || [];
  }

  private saveSyncQueue(): void {
    this.setItem('sync_queue', this.syncQueue);
  }

  private addToSyncQueue(operation: any): void {
    this.syncQueue.push({
      ...operation,
      timestamp: Date.now(),
      id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    });
    this.saveSyncQueue();
  }

  // Network status handlers
  private handleOnline(): void {
    this.isOnline = true;
    console.log('App is online - starting sync...');
    this.syncPendingOperations();
  }

  private handleOffline(): void {
    this.isOnline = false;
    console.log('App is offline - operations will be queued');
  }

  // Data operations
  async getData(key: string, fallbackData?: any): Promise<any> {
    // Try to get from localStorage first
    const localData = this.getItem(key);
    
    if (localData) {
      return localData;
    }
    
    // If online, try to fetch from API
    if (this.isOnline) {
      try {
        // This would be replaced with actual API calls
        const apiData = await this.fetchFromAPI(key);
        if (apiData) {
          this.setItem(key, apiData);
          return apiData;
        }
      } catch (error) {
        console.error('Error fetching from API:', error);
      }
    }
    
    // Return fallback data if provided
    return fallbackData || null;
  }

  async setData(key: string, data: any, syncToServer = true): Promise<void> {
    // Save to localStorage immediately
    this.setItem(key, data);
    
    // If online and sync is enabled, try to sync to server
    if (this.isOnline && syncToServer) {
      try {
        await this.syncToAPI(key, data);
      } catch (error) {
        console.error('Error syncing to API:', error);
        // Add to sync queue for later
        this.addToSyncQueue({
          type: 'UPDATE',
          key,
          data,
          operation: 'setData'
        });
      }
    } else if (syncToServer) {
      // Add to sync queue if offline
      this.addToSyncQueue({
        type: 'UPDATE',
        key,
        data,
        operation: 'setData'
      });
    }
  }

  async deleteData(key: string, syncToServer = true): Promise<void> {
    // Remove from localStorage
    this.removeItem(key);
    
    // If online and sync is enabled, try to sync to server
    if (this.isOnline && syncToServer) {
      try {
        await this.deleteFromAPI(key);
      } catch (error) {
        console.error('Error deleting from API:', error);
        // Add to sync queue for later
        this.addToSyncQueue({
          type: 'DELETE',
          key,
          operation: 'deleteData'
        });
      }
    } else if (syncToServer) {
      // Add to sync queue if offline
      this.addToSyncQueue({
        type: 'DELETE',
        key,
        operation: 'deleteData'
      });
    }
  }

  // Real API operations
  private async fetchFromAPI(key: string): Promise<any> {
    try {
      // Map storage keys to API endpoints
      const endpointMap: { [key: string]: string } = {
        'clients': '/api/clients',
        'patients': '/api/patients',
        'appointments': '/api/appointments',
        'staff': '/api/staff',
        'clinics': '/api/clinics',
        'invoices': '/api/invoices',
        'medical_records': '/api/medical-records',
        'services': '/api/services',
        'service_categories': '/api/service-categories',
        'species': '/api/species',
        'breeds': '/api/breeds',
        'roles': '/api/roles',
        'permissions': '/api/permissions'
      };

      const endpoint = endpointMap[key];
      if (!endpoint) {
        console.warn(`No API endpoint mapped for key: ${key}`);
        return null;
      }

      const response = await api.get(endpoint);
      return response.data.success ? response.data.data : null;
    } catch (error) {
      console.error(`Error fetching ${key} from API:`, error);
      return null;
    }
  }

  private async syncToAPI(key: string, data: any): Promise<void> {
    try {
      // Map storage keys to API endpoints
      const endpointMap: { [key: string]: string } = {
        'clients': '/api/clients',
        'patients': '/api/patients',
        'appointments': '/api/appointments',
        'staff': '/api/staff',
        'clinics': '/api/clinics',
        'invoices': '/api/invoices',
        'medical_records': '/api/medical-records',
        'services': '/api/services',
        'service_categories': '/api/service-categories',
        'species': '/api/species',
        'breeds': '/api/breeds'
      };

      const endpoint = endpointMap[key];
      if (!endpoint) {
        console.warn(`No API endpoint mapped for key: ${key}`);
        return;
      }

      // Determine if this is a create or update operation
      if (data.id || data._id) {
        // Update existing record
        await api.put(`${endpoint}/${data.id || data._id}`, data);
      } else {
        // Create new record
        await api.post(endpoint, data);
      }

      console.log(`Successfully synced ${key} to API`);
    } catch (error) {
      console.error(`Error syncing ${key} to API:`, error);
      throw error; // Re-throw to handle in sync queue
    }
  }

  private async deleteFromAPI(key: string): Promise<void> {
    try {
      // Extract the base key and ID from the key (e.g., "clients_123" -> "clients", "123")
      const parts = key.split('_');
      const baseKey = parts[0];
      const id = parts[1];

      const endpointMap: { [key: string]: string } = {
        'clients': '/api/clients',
        'patients': '/api/patients',
        'appointments': '/api/appointments',
        'staff': '/api/staff',
        'clinics': '/api/clinics',
        'invoices': '/api/invoices',
        'medical_records': '/api/medical-records',
        'services': '/api/services',
        'service_categories': '/api/service-categories',
        'species': '/api/species',
        'breeds': '/api/breeds'
      };

      const endpoint = endpointMap[baseKey];
      if (!endpoint || !id) {
        console.warn(`No API endpoint mapped for key: ${key} or missing ID`);
        return;
      }

      await api.delete(`${endpoint}/${id}`);
      console.log(`Successfully deleted ${key} from API`);
    } catch (error) {
      console.error(`Error deleting ${key} from API:`, error);
      throw error; // Re-throw to handle in sync queue
    }
  }

  // Sync pending operations
  async syncPendingOperations(): Promise<void> {
    if (!this.isOnline || this.syncQueue.length === 0) {
      return;
    }

    console.log(`Syncing ${this.syncQueue.length} pending operations...`);
    
    const operations = [...this.syncQueue];
    this.syncQueue = [];
    this.saveSyncQueue();

    for (const operation of operations) {
      try {
        switch (operation.type) {
          case 'UPDATE':
            await this.syncToAPI(operation.key, operation.data);
            break;
          case 'DELETE':
            await this.deleteFromAPI(operation.key);
            break;
        }
        console.log(`Synced operation: ${operation.type} ${operation.key}`);
      } catch (error) {
        console.error(`Failed to sync operation: ${operation.type} ${operation.key}`, error);
        // Re-add to sync queue
        this.syncQueue.push(operation);
      }
    }
    
    this.saveSyncQueue();
  }

  // Utility methods
  isOffline(): boolean {
    return !this.isOnline;
  }

  getPendingSyncCount(): number {
    return this.syncQueue.length;
  }

  clearAllData(): void {
    if (typeof window === 'undefined') return;
    
    const keys = Object.keys(localStorage).filter(key => 
      key.startsWith(this.storagePrefix)
    );
    
    keys.forEach(key => localStorage.removeItem(key));
    this.syncQueue = [];
  }

  // Export/Import for backup
  exportData(): any {
    if (typeof window === 'undefined') return {};
    
    const data: any = {};
    const keys = Object.keys(localStorage).filter(key => 
      key.startsWith(this.storagePrefix)
    );
    
    keys.forEach(key => {
      const cleanKey = key.replace(this.storagePrefix, '');
      data[cleanKey] = this.getItem(cleanKey);
    });
    
    return data;
  }

  importData(data: any): void {
    Object.keys(data).forEach(key => {
      this.setItem(key, data[key]);
    });
  }
}

// Create singleton instance
const offlineDataService = new OfflineDataService();

export default offlineDataService;
