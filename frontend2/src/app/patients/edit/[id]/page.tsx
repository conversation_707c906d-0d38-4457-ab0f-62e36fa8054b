'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { ArrowLeft, Save, Heart, AlertCircle } from 'lucide-react';
import DashboardLayout from '../../../../components/layout/DashboardLayout';
import MedicalLoader from '../../../../components/common/MedicalLoader';
import { useThemeClasses, useClinicTheme } from '../../../../contexts/ThemeContext';
import { cn } from '@/lib/utils';
import { PatientFormData } from '../../../../types';
import { patientsApi } from '../../../../lib/apiService';

export default function EditPatientPage() {
  const router = useRouter();
  const params = useParams();
  const patientId = params.id as string;
  const themeClasses = useThemeClasses();
  const { colors } = useClinicTheme();
  
  const [formData, setFormData] = useState<PatientFormData>({
    name: '',
    species: '',
    breed: '',
    dateOfBirth: '',
    gender: 'unknown',
    weight: 0,
    color: '',
    microchipId: '',
    clientId: '',
    medicalHistory: '',
    allergies: [],
    medications: [],
    notes: ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    const fetchPatient = async () => {
      try {
        const response = await patientsApi.getById(patientId);
        if (response.success) {
          setFormData(response.data);
        }
      } catch (error) {
        console.error('Error fetching patient:', error);
        // Set empty form data on error
        setFormData({
          name: '',
          species: '',
          breed: '',
          dateOfBirth: '',
          gender: '',
          weight: 0,
          color: '',
          microchipId: '',
          clientId: '',
          medicalHistory: '',
          allergies: [],
          medications: [],
          notes: ''
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (patientId) {
      fetchPatient();
    }
  }, [patientId]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Pet name is required';
    }

    if (!formData.species.trim()) {
      newErrors.species = 'Species is required';
    }

    if (!formData.breed.trim()) {
      newErrors.breed = 'Breed is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await patientsApi.update(patientId, formData);
      if (response.success) {
        router.push('/patients');
      }
    } catch (error) {
      console.error('Error updating patient:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <MedicalLoader
          message="Loading patient..."
          size="lg"
          fullScreen
        />
      </DashboardLayout>
    );
  }

  if (isSubmitting) {
    return (
      <DashboardLayout>
        <MedicalLoader
          message="Updating patient..."
          size="lg"
          fullScreen
        />
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className={cn('min-h-screen p-6', themeClasses.bgSecondary)}>
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="flex items-center gap-4 mb-8">
            <button
              onClick={() => router.back()}
              className={cn('p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700', themeClasses.text)}
            >
              <ArrowLeft className="h-5 w-5" />
            </button>
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-br from-pink-500 to-red-600 rounded-lg">
                <Heart className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className={cn('text-2xl font-bold', themeClasses.text)}>Edit Patient</h1>
                <p className={cn('text-sm', themeClasses.textSecondary)}>
                  Update patient information
                </p>
              </div>
            </div>
          </div>

          {/* Form */}
          <div className={cn('p-8 rounded-xl border', themeClasses.card)}>
            <form onSubmit={handleSubmit} className="space-y-8">
              {/* Basic Information */}
              <div>
                <h3 className={cn('text-lg font-semibold mb-6', themeClasses.text)}>Basic Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className={cn('block text-sm font-medium mb-2', themeClasses.text)}>
                      Pet Name *
                    </label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      className={cn(
                        'w-full px-4 py-3 rounded-lg border transition-all duration-200',
                        themeClasses.input,
                        errors.name ? 'border-red-500' : themeClasses.inputFocus
                      )}
                      placeholder="Enter pet name"
                    />
                    {errors.name && (
                      <p className="mt-2 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="h-4 w-4" />
                        {errors.name}
                      </p>
                    )}
                  </div>

                  <div>
                    <label className={cn('block text-sm font-medium mb-2', themeClasses.text)}>
                      Species *
                    </label>
                    <select
                      value={formData.species}
                      onChange={(e) => handleInputChange('species', e.target.value)}
                      className={cn(
                        'w-full px-4 py-3 rounded-lg border transition-all duration-200',
                        themeClasses.input,
                        errors.species ? 'border-red-500' : themeClasses.inputFocus
                      )}
                    >
                      <option value="">Select species</option>
                      <option value="Dog">Dog</option>
                      <option value="Cat">Cat</option>
                      <option value="Bird">Bird</option>
                      <option value="Rabbit">Rabbit</option>
                      <option value="Hamster">Hamster</option>
                      <option value="Guinea Pig">Guinea Pig</option>
                      <option value="Reptile">Reptile</option>
                      <option value="Fish">Fish</option>
                      <option value="Other">Other</option>
                    </select>
                    {errors.species && (
                      <p className="mt-2 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="h-4 w-4" />
                        {errors.species}
                      </p>
                    )}
                  </div>

                  <div>
                    <label className={cn('block text-sm font-medium mb-2', themeClasses.text)}>
                      Breed *
                    </label>
                    <input
                      type="text"
                      value={formData.breed}
                      onChange={(e) => handleInputChange('breed', e.target.value)}
                      className={cn(
                        'w-full px-4 py-3 rounded-lg border transition-all duration-200',
                        themeClasses.input,
                        errors.breed ? 'border-red-500' : themeClasses.inputFocus
                      )}
                      placeholder="Enter breed"
                    />
                    {errors.breed && (
                      <p className="mt-2 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="h-4 w-4" />
                        {errors.breed}
                      </p>
                    )}
                  </div>

                  <div>
                    <label className={cn('block text-sm font-medium mb-2', themeClasses.text)}>
                      Date of Birth
                    </label>
                    <input
                      type="date"
                      value={formData.dateOfBirth}
                      onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
                      className={cn('w-full px-4 py-3 rounded-lg border', themeClasses.input, themeClasses.inputFocus)}
                    />
                  </div>

                  <div>
                    <label className={cn('block text-sm font-medium mb-2', themeClasses.text)}>
                      Gender
                    </label>
                    <select
                      value={formData.gender}
                      onChange={(e) => handleInputChange('gender', e.target.value)}
                      className={cn('w-full px-4 py-3 rounded-lg border', themeClasses.input, themeClasses.inputFocus)}
                    >
                      <option value="male">Male</option>
                      <option value="female">Female</option>
                      <option value="unknown">Unknown</option>
                    </select>
                  </div>

                  <div>
                    <label className={cn('block text-sm font-medium mb-2', themeClasses.text)}>
                      Weight (lbs)
                    </label>
                    <input
                      type="number"
                      step="0.1"
                      value={formData.weight}
                      onChange={(e) => handleInputChange('weight', parseFloat(e.target.value) || 0)}
                      className={cn('w-full px-4 py-3 rounded-lg border', themeClasses.input, themeClasses.inputFocus)}
                      placeholder="Enter weight"
                    />
                  </div>
                </div>
              </div>

              {/* Form Actions */}
              <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700">
                <button
                  type="button"
                  onClick={() => router.back()}
                  className={cn('px-6 py-3 rounded-lg font-medium transition-colors', themeClasses.buttonSecondary)}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className={cn(
                    'flex items-center px-6 py-3 rounded-lg font-medium text-white transition-colors',
                    'disabled:opacity-50 disabled:cursor-not-allowed'
                  )}
                  style={{ backgroundColor: colors.primary }}
                >
                  <Save className="h-5 w-5 mr-2" />
                  {isSubmitting ? 'Updating...' : 'Update Patient'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
