import type { Metadata } from "next";
import "./globals.css";
import { ClinicBrandingProvider } from "../contexts/ClinicBrandingContext";
import { AuthProvider } from "../contexts/AuthContext";
import { ClinicDataProvider } from "../contexts/ClinicDataContext";
import { ThemeProvider } from "../contexts/ThemeContext";


export const metadata: Metadata = {
  title: "VetCare Pro - Veterinary Management System",
  description: "Comprehensive veterinary management SaaS platform for modern clinics",
  keywords: "veterinary, clinic management, pet care, appointments, medical records",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
      </head>
      <body className="antialiased" suppressHydrationWarning={true}>
        <ThemeProvider>
          <AuthProvider>
            <ClinicDataProvider>
              <ClinicBrandingProvider>

                {children}
              </ClinicBrandingProvider>
            </ClinicDataProvider>
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
