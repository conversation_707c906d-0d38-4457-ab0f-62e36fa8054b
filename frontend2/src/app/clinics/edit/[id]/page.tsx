'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { ArrowLeft, Save, Building, AlertCircle, Palette, Clock, Settings } from 'lucide-react';
import DashboardLayout from '../../../../components/layout/DashboardLayout';
import MedicalLoader from '../../../../components/common/MedicalLoader';
import { useAuth } from '../../../../contexts/AuthContext';
import { useThemeClasses, useClinicTheme } from '../../../../contexts/ThemeContext';
import { cn } from '@/lib/utils';
import { ClinicFormData } from '../../../../components/forms/ClinicForm';
import { clinicsApi } from '../../../../lib/apiService';

export default function EditClinicPage() {
  const router = useRouter();
  const params = useParams();
  const clinicId = params.id as string;
  const { hasRole } = useAuth();
  const themeClasses = useThemeClasses();
  const { colors } = useClinicTheme();
  
  // Check if user is admin or clinic owner
  const isAdmin = hasRole('admin');
  const isOwner = hasRole('owner');
  const canEdit = isAdmin || isOwner;
  
  const [formData, setFormData] = useState<ClinicFormData>({
    name: '',
    email: '',
    phone: '',
    website: '',
    address: {
      street: '',
      city: '',
      state: '',
      zipCode: '',
      country: 'USA'
    },
    businessHours: {
      monday: { open: '08:00', close: '18:00', closed: false },
      tuesday: { open: '08:00', close: '18:00', closed: false },
      wednesday: { open: '08:00', close: '18:00', closed: false },
      thursday: { open: '08:00', close: '18:00', closed: false },
      friday: { open: '08:00', close: '18:00', closed: false },
      saturday: { open: '09:00', close: '17:00', closed: false },
      sunday: { open: '10:00', close: '16:00', closed: true }
    },
    description: '',
    specialties: [],
    licenseNumber: '',
    taxId: '',
    isActive: true,
    branding: {
      primaryColor: '#3B82F6',
      secondaryColor: '#EC4899',
      accentColor: '#10B981',
      logo: ''
    },
    settings: {
      appointmentDuration: 30,
      bookingAdvanceLimit: 30,
      cancellationPolicy: 'Appointments must be cancelled at least 24 hours in advance.',
      timezone: 'America/New_York',
      currency: 'USD'
    }
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Redirect if no permission
  React.useEffect(() => {
    if (!canEdit) {
      router.push('/clinics');
    }
  }, [canEdit, router]);

  useEffect(() => {
    const fetchClinic = async () => {
      try {
        const response = await clinicsApi.getById(clinicId);
        if (response.success) {
          setFormData(response.data);
        }
      } catch (error) {
        console.error('Error fetching clinic:', error);
        // Set empty form data on error
        setFormData({
          name: '',
          email: '',
          phone: '',
          website: '',
          address: {
            street: '',
            city: '',
            state: '',
            zipCode: '',
            country: 'USA'
          },
          businessHours: {
            monday: { open: '08:00', close: '18:00', closed: false },
            tuesday: { open: '08:00', close: '18:00', closed: false },
            wednesday: { open: '08:00', close: '18:00', closed: false },
            thursday: { open: '08:00', close: '18:00', closed: false },
            friday: { open: '08:00', close: '18:00', closed: false },
            saturday: { open: '08:00', close: '18:00', closed: true },
            sunday: { open: '08:00', close: '18:00', closed: true }
          },
          description: '',
          specialties: [],
          licenseNumber: '',
          taxId: '',
          isActive: true,
          branding: {
            primaryColor: '#3B82F6',
            secondaryColor: '#EC4899',
            accentColor: '#10B981',
            logo: ''
          },
          settings: {
            appointmentDuration: 30,
            bookingAdvanceLimit: 30,
            cancellationPolicy: '',
            timezone: 'America/New_York',
            currency: 'USD'
          }
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (clinicId && canEdit) {
      fetchClinic();
    }
  }, [clinicId, canEdit]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Clinic name is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await clinicsApi.update(clinicId, formData);
      if (response.success) {
        router.push('/clinics');
      }
    } catch (error) {
      console.error('Error updating clinic:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...(prev[parent as keyof ClinicFormData] as any),
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));
    }

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  if (!canEdit) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h2 className={cn('text-xl font-semibold mb-2', themeClasses.text)}>Access Denied</h2>
            <p className={themeClasses.textSecondary}>You don't have permission to edit this clinic.</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <MedicalLoader
          message="Loading clinic..."
          size="lg"
          fullScreen
        />
      </DashboardLayout>
    );
  }

  if (isSubmitting) {
    return (
      <DashboardLayout>
        <MedicalLoader
          message="Updating clinic..."
          size="lg"
          fullScreen
        />
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className={cn('min-h-screen p-6', themeClasses.bgSecondary)}>
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="flex items-center gap-4 mb-8">
            <button
              onClick={() => router.back()}
              className={cn('p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700', themeClasses.text)}
            >
              <ArrowLeft className="h-5 w-5" />
            </button>
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg">
                <Building className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className={cn('text-2xl font-bold', themeClasses.text)}>Edit Clinic</h1>
                <p className={cn('text-sm', themeClasses.textSecondary)}>
                  Update clinic information
                </p>
              </div>
            </div>
          </div>

          {/* Form */}
          <div className={cn('p-8 rounded-xl border', themeClasses.card)}>
            <form onSubmit={handleSubmit} className="space-y-8">
              {/* Basic Information */}
              <div>
                <h3 className={cn('text-lg font-semibold mb-6', themeClasses.text)}>Basic Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className={cn('block text-sm font-medium mb-2', themeClasses.text)}>
                      Clinic Name *
                    </label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      className={cn(
                        'w-full px-4 py-3 rounded-lg border transition-all duration-200',
                        themeClasses.input,
                        errors.name ? 'border-red-500' : themeClasses.inputFocus
                      )}
                      placeholder="Enter clinic name"
                    />
                    {errors.name && (
                      <p className="mt-2 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="h-4 w-4" />
                        {errors.name}
                      </p>
                    )}
                  </div>

                  <div>
                    <label className={cn('block text-sm font-medium mb-2', themeClasses.text)}>
                      Email Address *
                    </label>
                    <input
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      className={cn(
                        'w-full px-4 py-3 rounded-lg border transition-all duration-200',
                        themeClasses.input,
                        errors.email ? 'border-red-500' : themeClasses.inputFocus
                      )}
                      placeholder="Enter email address"
                    />
                    {errors.email && (
                      <p className="mt-2 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="h-4 w-4" />
                        {errors.email}
                      </p>
                    )}
                  </div>

                  <div>
                    <label className={cn('block text-sm font-medium mb-2', themeClasses.text)}>
                      Phone Number *
                    </label>
                    <input
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      className={cn(
                        'w-full px-4 py-3 rounded-lg border transition-all duration-200',
                        themeClasses.input,
                        errors.phone ? 'border-red-500' : themeClasses.inputFocus
                      )}
                      placeholder="Enter phone number"
                    />
                    {errors.phone && (
                      <p className="mt-2 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="h-4 w-4" />
                        {errors.phone}
                      </p>
                    )}
                  </div>

                  <div>
                    <label className={cn('block text-sm font-medium mb-2', themeClasses.text)}>
                      Website
                    </label>
                    <input
                      type="url"
                      value={formData.website}
                      onChange={(e) => handleInputChange('website', e.target.value)}
                      className={cn('w-full px-4 py-3 rounded-lg border', themeClasses.input, themeClasses.inputFocus)}
                      placeholder="https://www.example.com"
                    />
                  </div>
                </div>

                <div className="mt-6">
                  <label className={cn('block text-sm font-medium mb-2', themeClasses.text)}>
                    Description
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    rows={3}
                    className={cn('w-full px-4 py-3 rounded-lg border', themeClasses.input, themeClasses.inputFocus)}
                    placeholder="Describe your clinic's services and specialties"
                  />
                </div>
              </div>

              {/* Address Information */}
              <div>
                <h3 className={cn('text-lg font-semibold mb-6', themeClasses.text)}>Address Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="md:col-span-2">
                    <label className={cn('block text-sm font-medium mb-2', themeClasses.text)}>
                      Street Address
                    </label>
                    <input
                      type="text"
                      value={formData.address.street}
                      onChange={(e) => handleInputChange('address.street', e.target.value)}
                      className={cn('w-full px-4 py-3 rounded-lg border', themeClasses.input, themeClasses.inputFocus)}
                      placeholder="Enter street address"
                    />
                  </div>

                  <div>
                    <label className={cn('block text-sm font-medium mb-2', themeClasses.text)}>
                      City
                    </label>
                    <input
                      type="text"
                      value={formData.address.city}
                      onChange={(e) => handleInputChange('address.city', e.target.value)}
                      className={cn('w-full px-4 py-3 rounded-lg border', themeClasses.input, themeClasses.inputFocus)}
                      placeholder="Enter city"
                    />
                  </div>

                  <div>
                    <label className={cn('block text-sm font-medium mb-2', themeClasses.text)}>
                      State
                    </label>
                    <input
                      type="text"
                      value={formData.address.state}
                      onChange={(e) => handleInputChange('address.state', e.target.value)}
                      className={cn('w-full px-4 py-3 rounded-lg border', themeClasses.input, themeClasses.inputFocus)}
                      placeholder="Enter state"
                    />
                  </div>

                  <div>
                    <label className={cn('block text-sm font-medium mb-2', themeClasses.text)}>
                      ZIP Code
                    </label>
                    <input
                      type="text"
                      value={formData.address.zipCode}
                      onChange={(e) => handleInputChange('address.zipCode', e.target.value)}
                      className={cn('w-full px-4 py-3 rounded-lg border', themeClasses.input, themeClasses.inputFocus)}
                      placeholder="Enter ZIP code"
                    />
                  </div>

                  <div>
                    <label className={cn('block text-sm font-medium mb-2', themeClasses.text)}>
                      Country
                    </label>
                    <select
                      value={formData.address.country}
                      onChange={(e) => handleInputChange('address.country', e.target.value)}
                      className={cn('w-full px-4 py-3 rounded-lg border', themeClasses.input, themeClasses.inputFocus)}
                    >
                      <option value="USA">United States</option>
                      <option value="Canada">Canada</option>
                      <option value="UK">United Kingdom</option>
                      <option value="Australia">Australia</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* Branding */}
              <div>
                <h3 className={cn('text-lg font-semibold mb-6 flex items-center gap-2', themeClasses.text)}>
                  <Palette className="h-5 w-5" />
                  Clinic Branding
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label className={cn('block text-sm font-medium mb-2', themeClasses.text)}>
                      Primary Color
                    </label>
                    <div className="flex items-center gap-3">
                      <input
                        type="color"
                        value={formData.branding.primaryColor}
                        onChange={(e) => handleInputChange('branding.primaryColor', e.target.value)}
                        className="w-12 h-12 rounded-lg border cursor-pointer"
                      />
                      <input
                        type="text"
                        value={formData.branding.primaryColor}
                        onChange={(e) => handleInputChange('branding.primaryColor', e.target.value)}
                        className={cn('flex-1 px-4 py-3 rounded-lg border', themeClasses.input, themeClasses.inputFocus)}
                        placeholder="#3B82F6"
                      />
                    </div>
                  </div>

                  <div>
                    <label className={cn('block text-sm font-medium mb-2', themeClasses.text)}>
                      Secondary Color
                    </label>
                    <div className="flex items-center gap-3">
                      <input
                        type="color"
                        value={formData.branding.secondaryColor}
                        onChange={(e) => handleInputChange('branding.secondaryColor', e.target.value)}
                        className="w-12 h-12 rounded-lg border cursor-pointer"
                      />
                      <input
                        type="text"
                        value={formData.branding.secondaryColor}
                        onChange={(e) => handleInputChange('branding.secondaryColor', e.target.value)}
                        className={cn('flex-1 px-4 py-3 rounded-lg border', themeClasses.input, themeClasses.inputFocus)}
                        placeholder="#EC4899"
                      />
                    </div>
                  </div>

                  <div>
                    <label className={cn('block text-sm font-medium mb-2', themeClasses.text)}>
                      Accent Color
                    </label>
                    <div className="flex items-center gap-3">
                      <input
                        type="color"
                        value={formData.branding.accentColor}
                        onChange={(e) => handleInputChange('branding.accentColor', e.target.value)}
                        className="w-12 h-12 rounded-lg border cursor-pointer"
                      />
                      <input
                        type="text"
                        value={formData.branding.accentColor}
                        onChange={(e) => handleInputChange('branding.accentColor', e.target.value)}
                        className={cn('flex-1 px-4 py-3 rounded-lg border', themeClasses.input, themeClasses.inputFocus)}
                        placeholder="#10B981"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Settings */}
              <div>
                <h3 className={cn('text-lg font-semibold mb-6 flex items-center gap-2', themeClasses.text)}>
                  <Settings className="h-5 w-5" />
                  Clinic Settings
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className={cn('block text-sm font-medium mb-2', themeClasses.text)}>
                      Default Appointment Duration (minutes)
                    </label>
                    <select
                      value={formData.settings.appointmentDuration}
                      onChange={(e) => handleInputChange('settings.appointmentDuration', parseInt(e.target.value))}
                      className={cn('w-full px-4 py-3 rounded-lg border', themeClasses.input, themeClasses.inputFocus)}
                    >
                      <option value={15}>15 minutes</option>
                      <option value={30}>30 minutes</option>
                      <option value={45}>45 minutes</option>
                      <option value={60}>1 hour</option>
                      <option value={90}>1.5 hours</option>
                      <option value={120}>2 hours</option>
                    </select>
                  </div>

                  <div>
                    <label className={cn('block text-sm font-medium mb-2', themeClasses.text)}>
                      Booking Advance Limit (days)
                    </label>
                    <input
                      type="number"
                      value={formData.settings.bookingAdvanceLimit}
                      onChange={(e) => handleInputChange('settings.bookingAdvanceLimit', parseInt(e.target.value))}
                      className={cn('w-full px-4 py-3 rounded-lg border', themeClasses.input, themeClasses.inputFocus)}
                      placeholder="30"
                      min="1"
                      max="365"
                    />
                  </div>

                  <div>
                    <label className={cn('block text-sm font-medium mb-2', themeClasses.text)}>
                      Timezone
                    </label>
                    <select
                      value={formData.settings.timezone}
                      onChange={(e) => handleInputChange('settings.timezone', e.target.value)}
                      className={cn('w-full px-4 py-3 rounded-lg border', themeClasses.input, themeClasses.inputFocus)}
                    >
                      <option value="America/New_York">Eastern Time</option>
                      <option value="America/Chicago">Central Time</option>
                      <option value="America/Denver">Mountain Time</option>
                      <option value="America/Los_Angeles">Pacific Time</option>
                      <option value="UTC">UTC</option>
                    </select>
                  </div>

                  <div>
                    <label className={cn('block text-sm font-medium mb-2', themeClasses.text)}>
                      Currency
                    </label>
                    <select
                      value={formData.settings.currency}
                      onChange={(e) => handleInputChange('settings.currency', e.target.value)}
                      className={cn('w-full px-4 py-3 rounded-lg border', themeClasses.input, themeClasses.inputFocus)}
                    >
                      <option value="USD">USD - US Dollar</option>
                      <option value="CAD">CAD - Canadian Dollar</option>
                      <option value="EUR">EUR - Euro</option>
                      <option value="GBP">GBP - British Pound</option>
                      <option value="AUD">AUD - Australian Dollar</option>
                    </select>
                  </div>

                  <div className="md:col-span-2">
                    <label className={cn('block text-sm font-medium mb-2', themeClasses.text)}>
                      Cancellation Policy
                    </label>
                    <textarea
                      value={formData.settings.cancellationPolicy}
                      onChange={(e) => handleInputChange('settings.cancellationPolicy', e.target.value)}
                      rows={3}
                      className={cn('w-full px-4 py-3 rounded-lg border', themeClasses.input, themeClasses.inputFocus)}
                      placeholder="Enter your clinic's cancellation policy"
                    />
                  </div>
                </div>
              </div>

              {/* Form Actions */}
              <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700">
                <button
                  type="button"
                  onClick={() => router.back()}
                  className={cn('px-6 py-3 rounded-lg font-medium transition-colors', themeClasses.buttonSecondary)}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className={cn(
                    'flex items-center px-6 py-3 rounded-lg font-medium text-white transition-colors',
                    'disabled:opacity-50 disabled:cursor-not-allowed'
                  )}
                  style={{ backgroundColor: colors.primary }}
                >
                  <Save className="h-5 w-5 mr-2" />
                  {isSubmitting ? 'Updating...' : 'Update Clinic'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
