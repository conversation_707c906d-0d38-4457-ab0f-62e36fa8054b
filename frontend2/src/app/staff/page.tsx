'use client';

import React, { useState, useEffect } from 'react';
import {
  Users,
  Plus,
  Search,
  Filter,
  Edit3,
  Trash2,
  MoreVertical,
  UserCheck,
  UserX,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Shield
} from 'lucide-react';
import DashboardLayout from '../../components/layout/DashboardLayout';
import { useThemeClasses, useClinicTheme } from '../../contexts/ThemeContext';
import { useAuth } from '../../contexts/AuthContext';
import { api, endpoints } from '@/lib/api';
import { cn } from '@/lib/utils';

interface StaffMember {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  role: string;
  avatar?: string;
  isActive: boolean;
  joinedAt: string;
  specializations?: string[];
  address?: string;
}

export default function StaffPage() {
  const themeClasses = useThemeClasses();
  const { isDark, colors } = useClinicTheme();
  const { user, currentClinic } = useAuth();

  const [staff, setStaff] = useState<StaffMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterRole, setFilterRole] = useState('all');
  const [showAddModal, setShowAddModal] = useState(false);

  // Fetch staff data from API
  useEffect(() => {
    const fetchStaff = async () => {
      try {
        setLoading(true);

        if (!currentClinic?.id) {
          setStaff([]);
          return;
        }

        // Fetch staff for the current clinic
        const response = await api.get(endpoints.clinics.staff(currentClinic.id));

        if (response.data.success) {
          // Transform API data to match UI interface
          const transformedStaff: StaffMember[] = response.data.data.map((member: any) => ({
            id: member.id,
            firstName: member.user?.firstName || '',
            lastName: member.user?.lastName || '',
            email: member.user?.email || '',
            phone: member.user?.phone || '',
            role: member.role || 'staff',
            avatar: member.user?.avatar,
            isActive: member.isActive !== false,
            joinedAt: member.createdAt || new Date().toISOString(),
            specializations: member.specializations || [],
            address: member.address || ''
          }));

          setStaff(transformedStaff);
        }
      } catch (error) {
        console.error('Error fetching staff:', error);
        // Set empty array on error
        setStaff([]);
      } finally {
        setLoading(false);
      }
    };

    fetchStaff();
  }, [currentClinic?.id]);



  const filteredStaff = staff.filter(member => {
    const matchesSearch = !searchTerm ||
      member.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      member.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      member.email.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesRole = filterRole === 'all' || member.role.toLowerCase().includes(filterRole.toLowerCase());

    return matchesSearch && matchesRole;
  });

  const getRoleBadgeColor = (role: string) => {
    switch (role.toLowerCase()) {
      case 'veterinarian': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'vet technician': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'receptionist': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
    }
  };

  const StaffCard = ({ member }: { member: StaffMember }) => (
    <div className={cn('p-6 rounded-xl border transition-all duration-200 hover:shadow-lg', themeClasses.card)}>
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-4">
          <div className="h-12 w-12 rounded-full bg-blue-500 flex items-center justify-center text-white text-lg font-semibold">
            {member.avatar ? (
              <img 
                src={member.avatar} 
                alt={`${member.firstName} ${member.lastName}`}
                className="h-12 w-12 rounded-full object-cover"
              />
            ) : (
              `${member.firstName.charAt(0)}${member.lastName.charAt(0)}`
            )}
          </div>
          <div>
            <h3 className={cn('text-lg font-semibold', themeClasses.text)}>
              {member.firstName} {member.lastName}
            </h3>
            <span className={cn('inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium', getRoleBadgeColor(member.role))}>
              {member.role}
            </span>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <span className={cn(
            'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
            member.isActive 
              ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
              : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
          )}>
            {member.isActive ? (
              <>
                <UserCheck className="h-3 w-3 mr-1" />
                Active
              </>
            ) : (
              <>
                <UserX className="h-3 w-3 mr-1" />
                Inactive
              </>
            )}
          </span>
          <button className={cn('p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700', themeClasses.buttonGhost)}>
            <MoreVertical className="h-4 w-4" />
          </button>
        </div>
      </div>

      <div className="space-y-3">
        <div className="flex items-center space-x-2 text-sm">
          <Mail className="h-4 w-4 text-gray-400" />
          <span className={themeClasses.textSecondary}>{member.email}</span>
        </div>
        <div className="flex items-center space-x-2 text-sm">
          <Phone className="h-4 w-4 text-gray-400" />
          <span className={themeClasses.textSecondary}>{member.phone}</span>
        </div>
        {member.address && (
          <div className="flex items-center space-x-2 text-sm">
            <MapPin className="h-4 w-4 text-gray-400" />
            <span className={themeClasses.textSecondary}>{member.address}</span>
          </div>
        )}
        <div className="flex items-center space-x-2 text-sm">
          <Calendar className="h-4 w-4 text-gray-400" />
          <span className={themeClasses.textSecondary}>
            Joined {new Date(member.joinedAt).toLocaleDateString()}
          </span>
        </div>
      </div>

      {member.specializations && member.specializations.length > 0 && (
        <div className="mt-4">
          <p className={cn('text-sm font-medium mb-2', themeClasses.text)}>Specializations:</p>
          <div className="flex flex-wrap gap-1">
            {member.specializations.map((spec, index) => (
              <span 
                key={index}
                className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
              >
                {spec}
              </span>
            ))}
          </div>
        </div>
      )}

      <div className="mt-4 flex space-x-2">
        <button
          className={cn(
            'flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors',
            themeClasses.buttonSecondary
          )}
        >
          <Edit3 className="h-4 w-4 mr-2" />
          Edit
        </button>
        <button
          className="flex items-center px-3 py-2 rounded-lg text-sm font-medium text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
        >
          <Trash2 className="h-4 w-4 mr-2" />
          Remove
        </button>
      </div>
    </div>
  );

  if (loading) {
    return (
      <DashboardLayout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="flex flex-col items-center space-y-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2" style={{ borderColor: colors.primary }}></div>
            <p className={themeClasses.textSecondary}>Loading staff...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div 
        className={cn('min-h-screen p-6', themeClasses.bgSecondary)}
        style={{
          background: isDark 
            ? `linear-gradient(135deg, ${colors.primary}05, ${colors.secondary}03), ${themeClasses.bgSecondary.replace('bg-', '')}`
            : `linear-gradient(135deg, ${colors.primary}08, ${colors.secondary}05), ${themeClasses.bgSecondary.replace('bg-', '')}`
        }}
      >
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className={cn('text-3xl font-bold', themeClasses.text)}>Staff Management</h1>
              <p className={themeClasses.textSecondary}>
                Manage your clinic staff members and their roles
              </p>
            </div>
            <button
              onClick={() => setShowAddModal(true)}
              className={cn(
                'flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all duration-200',
                'text-white hover:shadow-lg transform hover:-translate-y-0.5'
              )}
              style={{ backgroundColor: colors.primary }}
            >
              <Plus className="h-5 w-5" />
              Add Staff Member
            </button>
          </div>

          {/* Filters */}
          <div className={cn('p-6 rounded-xl border mb-6', themeClasses.card)}>
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search staff members..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className={cn(
                      'w-full pl-10 pr-4 py-3 rounded-lg border transition-all duration-200',
                      themeClasses.input,
                      themeClasses.inputFocus
                    )}
                  />
                </div>
              </div>
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <Filter className="h-5 w-5 text-gray-400" />
                  <select
                    value={filterRole}
                    onChange={(e) => setFilterRole(e.target.value)}
                    className={cn(
                      'px-3 py-2 rounded-lg border transition-all duration-200',
                      themeClasses.input,
                      themeClasses.inputFocus
                    )}
                  >
                    <option value="all">All Roles</option>
                    <option value="veterinarian">Veterinarians</option>
                    <option value="technician">Technicians</option>
                    <option value="receptionist">Receptionists</option>
                  </select>
                </div>
              </div>
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className={cn('p-6 rounded-xl border', themeClasses.card)}>
              <div className="flex items-center justify-between">
                <div>
                  <p className={cn('text-sm font-medium', themeClasses.textSecondary)}>Total Staff</p>
                  <p className={cn('text-2xl font-bold', themeClasses.text)}>{staff.length}</p>
                </div>
                <Users className="h-8 w-8" style={{ color: colors.primary }} />
              </div>
            </div>
            <div className={cn('p-6 rounded-xl border', themeClasses.card)}>
              <div className="flex items-center justify-between">
                <div>
                  <p className={cn('text-sm font-medium', themeClasses.textSecondary)}>Active</p>
                  <p className={cn('text-2xl font-bold', themeClasses.text)}>
                    {staff.filter(s => s.isActive).length}
                  </p>
                </div>
                <UserCheck className="h-8 w-8 text-green-500" />
              </div>
            </div>
            <div className={cn('p-6 rounded-xl border', themeClasses.card)}>
              <div className="flex items-center justify-between">
                <div>
                  <p className={cn('text-sm font-medium', themeClasses.textSecondary)}>Veterinarians</p>
                  <p className={cn('text-2xl font-bold', themeClasses.text)}>
                    {staff.filter(s => s.role === 'Veterinarian').length}
                  </p>
                </div>
                <Shield className="h-8 w-8 text-purple-500" />
              </div>
            </div>
            <div className={cn('p-6 rounded-xl border', themeClasses.card)}>
              <div className="flex items-center justify-between">
                <div>
                  <p className={cn('text-sm font-medium', themeClasses.textSecondary)}>Technicians</p>
                  <p className={cn('text-2xl font-bold', themeClasses.text)}>
                    {staff.filter(s => s.role === 'Vet Technician').length}
                  </p>
                </div>
                <Users className="h-8 w-8 text-blue-500" />
              </div>
            </div>
          </div>

          {/* Staff Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredStaff.map((member) => (
              <StaffCard key={member.id} member={member} />
            ))}
          </div>

          {filteredStaff.length === 0 && (
            <div className="text-center py-12">
              <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className={cn('text-lg font-medium mb-2', themeClasses.text)}>No staff members found</h3>
              <p className={themeClasses.textSecondary}>
                {searchTerm || filterRole !== 'all' 
                  ? 'Try adjusting your search or filter criteria'
                  : 'Get started by adding your first staff member'
                }
              </p>
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
