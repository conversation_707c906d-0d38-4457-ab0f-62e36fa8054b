'use client';

import React, { useState, useEffect } from 'react';
import {
  Users,
  Plus,
  Search,
  Filter,
  Edit3,
  Trash2,
  MoreVertical,
  UserCheck,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Shield,
  Award,
  Stethoscope,
  GraduationCap,
  Star,
  Clock,
  Activity
} from 'lucide-react';
import DashboardLayout from '../../../components/layout/DashboardLayout';
import Table, { TableColumn, PaginationInfo } from '../../../components/common/Table';
import ActionDropdown, { ActionItem } from '../../../components/common/ActionDropdown';
import MedicalLoader from '../../../components/common/MedicalLoader';
import SearchInput from '../../../components/common/SearchInput';
import StaffForm from '../../../components/forms/StaffForm';
import { useThemeClasses, useClinicTheme } from '../../../contexts/ThemeContext';
import { useAuth } from '../../../contexts/AuthContext';
import { api, endpoints } from '@/lib/api';
import { cn } from '@/lib/utils';
import { staffApi, QueryParams } from '../../../lib/apiService';

interface Veterinarian {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  avatar?: string;
  isActive: boolean;
  joinedAt: string;
  specializations: string[];
  qualifications: string[];
  licenseNumber: string;
  yearsExperience: number;
  rating: number;
  totalPatients: number;
  monthlyAppointments: number;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  emergencyContact: {
    name: string;
    relationship: string;
    phone: string;
  };
  schedule: {
    monday: { start: string; end: string; isWorking: boolean };
    tuesday: { start: string; end: string; isWorking: boolean };
    wednesday: { start: string; end: string; isWorking: boolean };
    thursday: { start: string; end: string; isWorking: boolean };
    friday: { start: string; end: string; isWorking: boolean };
    saturday: { start: string; end: string; isWorking: boolean };
    sunday: { start: string; end: string; isWorking: boolean };
  };
  salary: number;
  notes?: string;
}

export default function VeterinariansPage() {
  const themeClasses = useThemeClasses();
  const { colors } = useClinicTheme();
  
  const [veterinarians, setVeterinarians] = useState<Veterinarian[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [showAddModal, setShowAddModal] = useState(false);
  const [selectedVet, setSelectedVet] = useState<Veterinarian | null>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);

  // Fetch veterinarians data from API
  useEffect(() => {
    const fetchVeterinarians = async () => {
      try {
        setLoading(true);

        // Build query parameters
        const queryParams = new URLSearchParams({
          page: '1',
          limit: '50',
          role: 'veterinarian', // Filter for veterinarians only
          ...(searchTerm && { search: searchTerm }),
          ...(filterStatus !== 'all' && { status: filterStatus })
        });

        // Fetch staff with veterinarian role
        const response = await api.get(`/api/staff?${queryParams}`);

        if (response.data.success) {
          // Transform API data to match UI interface
          const transformedVeterinarians: Veterinarian[] = response.data.data.map((vet: any) => ({
            id: vet.id,
            firstName: vet.user?.firstName || '',
            lastName: vet.user?.lastName || '',
            email: vet.user?.email || '',
            phone: vet.user?.phone || '',
            avatar: vet.user?.avatar,
            isActive: vet.isActive !== false,
            joinedAt: vet.createdAt || new Date().toISOString(),
            specializations: vet.specializations || [],
            qualifications: vet.qualifications || [],
            licenseNumber: vet.licenseNumber || '',
            yearsExperience: vet.yearsExperience || 0,
            rating: vet.rating || 0,
            totalPatients: vet.totalPatients || 0,
            monthlyAppointments: vet.monthlyAppointments || 0,
            address: vet.address || {
              street: '',
              city: '',
              state: '',
              zipCode: '',
              country: ''
            },
            emergencyContact: vet.emergencyContact || {
              name: '',
              relationship: '',
              phone: ''
            },
            schedule: vet.schedule || {
              monday: { start: '08:00', end: '17:00', isWorking: true },
              tuesday: { start: '08:00', end: '17:00', isWorking: true },
              wednesday: { start: '08:00', end: '17:00', isWorking: true },
              thursday: { start: '08:00', end: '17:00', isWorking: true },
              friday: { start: '08:00', end: '17:00', isWorking: true },
              saturday: { start: '08:00', end: '12:00', isWorking: false },
              sunday: { start: '00:00', end: '00:00', isWorking: false }
            },
            hourlyRate: vet.hourlyRate || 0,
            notes: vet.notes || ''
          }));

          setVeterinarians(transformedVeterinarians);
        }
      } catch (error) {
        console.error('Error fetching veterinarians:', error);
        // Set empty array on error
        setVeterinarians([]);
      } finally {
        setLoading(false);
      }
    };

    fetchVeterinarians();
  }, [searchTerm, filterStatus]);

  // Handle search with debounce
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchTerm.length >= 3 || searchTerm.length === 0) {
        // Trigger fetch when search term changes
      }
    }, 1000);

    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  // Remove the mock data array that was here before
    {
      id: 1,
      firstName: 'Dr. Michael',
      lastName: 'Thompson',
      email: '<EMAIL>',
      phone: '+****************',
      avatar: '/avatars/dr-thompson.jpg',
      isActive: true,
      joinedAt: '2022-03-15T00:00:00Z',
      specializations: ['Small Animal Medicine', 'Surgery', 'Emergency Care'],
      qualifications: ['DVM - UC Davis', 'Board Certified Surgeon', 'Emergency Medicine Certification'],
      licenseNumber: 'VET-CA-12345',
      yearsExperience: 8,
      rating: 4.9,
      totalPatients: 1250,
      monthlyAppointments: 180,
      address: {
        street: '123 Doctor Lane',
        city: 'Springfield',
        state: 'CA',
        zipCode: '90210',
        country: 'USA'
      },
      emergencyContact: {
        name: 'Sarah Thompson',
        relationship: 'Spouse',
        phone: '+****************'
      },
      schedule: {
        monday: { start: '08:00', end: '18:00', isWorking: true },
        tuesday: { start: '08:00', end: '18:00', isWorking: true },
        wednesday: { start: '08:00', end: '18:00', isWorking: true },
        thursday: { start: '08:00', end: '18:00', isWorking: true },
        friday: { start: '08:00', end: '16:00', isWorking: true },
        saturday: { start: '09:00', end: '13:00', isWorking: true },
        sunday: { start: '00:00', end: '00:00', isWorking: false }
      },
      salary: 120000,
      notes: 'Senior veterinarian with excellent surgical skills. Preferred for complex cases.'
    },
    {
      id: 2,
      firstName: 'Dr. Jennifer',
      lastName: 'Rodriguez',
      email: '<EMAIL>',
      phone: '+****************',
      avatar: '/avatars/dr-rodriguez.jpg',
      isActive: true,
      joinedAt: '2023-01-10T00:00:00Z',
      specializations: ['Exotic Animals', 'Emergency Medicine', 'Cardiology'],
      qualifications: ['DVM - Cornell University', 'Exotic Animal Specialist', 'Cardiology Certification'],
      licenseNumber: 'VET-CA-67890',
      yearsExperience: 5,
      rating: 4.8,
      totalPatients: 890,
      monthlyAppointments: 150,
      address: {
        street: '456 Medical Drive',
        city: 'Springfield',
        state: 'CA',
        zipCode: '90210',
        country: 'USA'
      },
      emergencyContact: {
        name: 'Carlos Rodriguez',
        relationship: 'Brother',
        phone: '+****************'
      },
      schedule: {
        monday: { start: '09:00', end: '17:00', isWorking: true },
        tuesday: { start: '09:00', end: '17:00', isWorking: true },
        wednesday: { start: '09:00', end: '17:00', isWorking: true },
        thursday: { start: '09:00', end: '17:00', isWorking: true },
        friday: { start: '09:00', end: '17:00', isWorking: true },
        saturday: { start: '00:00', end: '00:00', isWorking: false },
        sunday: { start: '00:00', end: '00:00', isWorking: false }
      },
      salary: 95000,
      notes: 'Specializes in exotic pets and emergency cases. Excellent with difficult animals.'
    }
  ];



  const filteredVeterinarians = veterinarians.filter(vet => {
    const matchesSearch = !searchTerm ||
      vet.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vet.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vet.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vet.specializations.some(spec => spec.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesStatus = filterStatus === 'all' || 
      (filterStatus === 'active' && vet.isActive) ||
      (filterStatus === 'inactive' && !vet.isActive);

    return matchesSearch && matchesStatus;
  });

  const columns: TableColumn[] = [
    {
      key: 'name',
      label: 'Veterinarian',
      sortable: true,
      render: (_, vet: Veterinarian) => (
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-semibold">
            {vet.avatar ? (
              <img src={vet.avatar} alt={`${vet.firstName} ${vet.lastName}`} className="w-10 h-10 rounded-full object-cover" />
            ) : (
              `${vet.firstName.charAt(0)}${vet.lastName.charAt(0)}`
            )}
          </div>
          <div>
            <div className={cn('font-medium', themeClasses.text)}>
              {vet.firstName} {vet.lastName}
            </div>
            <div className={cn('text-sm', themeClasses.textSecondary)}>
              License: {vet.licenseNumber}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'contact',
      label: 'Contact',
      render: (_, vet: Veterinarian) => (
        <div className="space-y-1">
          <div className={cn('text-sm', themeClasses.text)}>{vet.email}</div>
          <div className={cn('text-sm', themeClasses.textSecondary)}>{vet.phone}</div>
        </div>
      )
    },
    {
      key: 'specializations',
      label: 'Specializations',
      render: (_, vet: Veterinarian) => (
        <div className="flex flex-wrap gap-1">
          {vet.specializations.slice(0, 2).map((spec, index) => (
            <span
              key={index}
              className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
            >
              {spec}
            </span>
          ))}
          {vet.specializations.length > 2 && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
              +{vet.specializations.length - 2} more
            </span>
          )}
        </div>
      )
    },
    {
      key: 'experience',
      label: 'Experience',
      sortable: true,
      render: (_, vet: Veterinarian) => (
        <div className="space-y-1">
          <div className={cn('text-sm font-medium', themeClasses.text)}>
            {vet.yearsExperience} years
          </div>
          <div className="flex items-center space-x-1">
            <Star className="h-4 w-4 text-yellow-400 fill-current" />
            <span className={cn('text-sm', themeClasses.textSecondary)}>
              {vet.rating}/5.0
            </span>
          </div>
        </div>
      )
    },
    {
      key: 'patients',
      label: 'Patients',
      sortable: true,
      render: (_, vet: Veterinarian) => (
        <div className="space-y-1">
          <div className={cn('text-sm font-medium', themeClasses.text)}>
            {vet.totalPatients.toLocaleString()}
          </div>
          <div className={cn('text-xs', themeClasses.textSecondary)}>
            {vet.monthlyAppointments}/month
          </div>
        </div>
      )
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (_, vet: Veterinarian) => (
        <span
          className={cn(
            'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
            vet.isActive
              ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
              : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
          )}
        >
          {vet.isActive ? 'Active' : 'Inactive'}
        </span>
      )
    }
  ];

  const actionItems: ActionItem[] = [
    {
      label: 'View Details',
      icon: UserCheck,
      onClick: (vet: Veterinarian) => {
        setSelectedVet(vet);
        setShowDetailsModal(true);
      }
    },
    {
      label: 'Edit',
      icon: Edit3,
      onClick: (vet: Veterinarian) => {
        setSelectedVet(vet);
        setShowAddModal(true);
      }
    },
    {
      label: 'Deactivate',
      icon: Trash2,
      onClick: (vet: Veterinarian) => {
        // Handle deactivation
        console.log('Deactivate veterinarian:', vet.id);
      },
      variant: 'danger'
    }
  ];

  const paginationInfo: PaginationInfo = {
    currentPage: 1,
    totalPages: Math.ceil(filteredVeterinarians.length / 10),
    totalItems: filteredVeterinarians.length,
    itemsPerPage: 10
  };

  if (loading) {
    return (
      <DashboardLayout>
        <MedicalLoader
          message="Loading veterinarians..."
          size="lg"
          fullScreen
        />
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className={cn('min-h-screen p-6', themeClasses.bgSecondary)}>
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className={cn('text-3xl font-bold', themeClasses.text)}>Veterinarians</h1>
              <p className={themeClasses.textSecondary}>
                Manage veterinarian staff, specializations, and schedules
              </p>
            </div>
            <button
              onClick={() => setShowAddModal(true)}
              className={cn(
                'flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all duration-200',
                'text-white hover:shadow-lg transform hover:-translate-y-0.5'
              )}
              style={{ backgroundColor: colors.primary }}
            >
              <Plus className="h-5 w-5" />
              Add Veterinarian
            </button>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className={cn('p-6 rounded-xl border', themeClasses.card)}>
              <div className="flex items-center justify-between">
                <div>
                  <p className={cn('text-sm font-medium', themeClasses.textSecondary)}>Total Veterinarians</p>
                  <p className={cn('text-2xl font-bold', themeClasses.text)}>{veterinarians.length}</p>
                </div>
                <Stethoscope className="h-8 w-8" style={{ color: colors.primary }} />
              </div>
            </div>
            <div className={cn('p-6 rounded-xl border', themeClasses.card)}>
              <div className="flex items-center justify-between">
                <div>
                  <p className={cn('text-sm font-medium', themeClasses.textSecondary)}>Active</p>
                  <p className={cn('text-2xl font-bold', themeClasses.text)}>
                    {veterinarians.filter(v => v.isActive).length}
                  </p>
                </div>
                <UserCheck className="h-8 w-8 text-green-500" />
              </div>
            </div>
            <div className={cn('p-6 rounded-xl border', themeClasses.card)}>
              <div className="flex items-center justify-between">
                <div>
                  <p className={cn('text-sm font-medium', themeClasses.textSecondary)}>Avg Experience</p>
                  <p className={cn('text-2xl font-bold', themeClasses.text)}>
                    {Math.round(veterinarians.reduce((acc, v) => acc + v.yearsExperience, 0) / veterinarians.length || 0)} years
                  </p>
                </div>
                <Award className="h-8 w-8 text-yellow-500" />
              </div>
            </div>
            <div className={cn('p-6 rounded-xl border', themeClasses.card)}>
              <div className="flex items-center justify-between">
                <div>
                  <p className={cn('text-sm font-medium', themeClasses.textSecondary)}>Total Patients</p>
                  <p className={cn('text-2xl font-bold', themeClasses.text)}>
                    {veterinarians.reduce((acc, v) => acc + v.totalPatients, 0).toLocaleString()}
                  </p>
                </div>
                <Activity className="h-8 w-8 text-blue-500" />
              </div>
            </div>
          </div>

          {/* Filters */}
          <div className={cn('p-6 rounded-xl border mb-6', themeClasses.card)}>
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search veterinarians by name, email, or specialization..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className={cn(
                      'w-full pl-10 pr-4 py-3 rounded-lg border transition-all duration-200',
                      themeClasses.input,
                      themeClasses.inputFocus
                    )}
                  />
                </div>
              </div>
              <div className="flex items-center gap-4">
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className={cn(
                    'px-3 py-2 rounded-lg border transition-all duration-200',
                    themeClasses.input,
                    themeClasses.inputFocus
                  )}
                >
                  <option value="all">All Status</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
              </div>
            </div>
          </div>

          {/* Table */}
          <Table
            data={filteredVeterinarians}
            columns={columns}
            pagination={paginationInfo}
            actions={actionItems}
            onPageChange={(page) => console.log('Page changed:', page)}
            onSort={(column, direction) => console.log('Sort:', column, direction)}
          />
        </div>
      </div>
    </DashboardLayout>
  );
}
